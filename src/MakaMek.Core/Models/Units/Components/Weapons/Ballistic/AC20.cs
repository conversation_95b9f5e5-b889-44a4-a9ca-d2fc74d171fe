using Sanet.MakaMek.Core.Data.Units;

namespace Sanet.MakaMek.Core.Models.Units.Components.Weapons.Ballistic;

public class Ac20() : Weapon(Definition)
{
    // Static definition for this weapon type
    public static readonly WeaponDefinition Definition = new(
        Name: "AC/20",
        ElementaryDamage: 20,
        Heat: 7,
        MinimumRange: 0,
        ShortRange: 3,
        MediumRange: 6,
        <PERSON>Range: 9,
        Type: WeaponType.Ballistic,
        BattleValue: 178,
        Size: 10,
        FullAmmoRounds: 5,
        WeaponComponentType: MakaMekComponent.AC20,
        AmmoComponentType: MakaMekComponent.ISAmmoAC20);
        
    // Constructor uses the static definition
    public static Ammo CreateAmmo()
    {
        return new Ammo(Definition, Definition.FullAmmoRounds);
    }
}
