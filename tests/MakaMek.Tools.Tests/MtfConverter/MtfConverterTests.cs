using System.CommandLine;
using System.Text.Json;
using Shouldly;
using Xunit;
using Sanet.MakaMek.Core.Data.Units;
using Sanet.MakaMek.Core.Models.Units;

namespace MakaMek.Tools.MtfConverter.Tests;

public class MtfConverterTests : IDisposable
{
    private readonly string _testDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestData");
    private readonly string _tempOutputPath = Path.Combine(Path.GetTempPath(), "MtfConverterTests", Guid.NewGuid().ToString());

    public MtfConverterTests()
    {
        // Ensure temp output directory exists
        Directory.CreateDirectory(_tempOutputPath);
    }

    [Fact]
    public async Task ConvertSingleMtfFile_ShouldCreateJsonFile()
    {
        // Arrange
        var inputFile = Path.Combine(_testDataPath, "LCT-1V.mtf");
        var expectedOutputFile = Path.Combine(_tempOutputPath, "LCT-1V.json");

        // Act
        var result = await RunConverter(["-i", inputFile, "-o", _tempOutputPath]);

        // Assert
        result.ShouldBe(0, "Converter should exit with success code");
        File.Exists(expectedOutputFile).ShouldBeTrue("JSON output file should be created");

        // Verify JSON content
        var jsonContent = await File.ReadAllTextAsync(expectedOutputFile);
        var unitData = JsonSerializer.Deserialize<UnitData>(jsonContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        unitData.Chassis.ShouldBe("Locust");
        unitData.Model.ShouldBe("LCT-1V");
        unitData.Mass.ShouldBe(20);
        unitData.WalkMp.ShouldBe(8);
        unitData.EngineRating.ShouldBe(160);
        unitData.EngineType.ShouldBe("Fusion");
    }

    [Fact]
    public async Task ConvertDirectory_ShouldCreateMultipleJsonFiles()
    {
        // Arrange
        var inputDirectory = _testDataPath;

        // Act
        var result = await RunConverter(["-i", inputDirectory, "-o", _tempOutputPath]);

        // Assert
        result.ShouldBe(0, "Converter should exit with success code");

        var jsonFiles = Directory.GetFiles(_tempOutputPath, "*.json");
        jsonFiles.Length.ShouldBeGreaterThan(0, "Should create at least one JSON file");

        // Verify at least one file was converted correctly
        var locustJsonFile = Path.Combine(_tempOutputPath, "LCT-1V.json");
        File.Exists(locustJsonFile).ShouldBeTrue("LCT-1V.json should be created");
    }

    [Fact]
    public async Task ConvertNonExistentFile_ShouldReturnErrorCode()
    {
        // Arrange
        var nonExistentFile = Path.Combine(_tempOutputPath, "NonExistent.mtf");

        // Act
        var result = await RunConverter(["-i", nonExistentFile, "-o", _tempOutputPath]);

        // Assert
        result.ShouldBe(1, "Converter should exit with error code for non-existent file");
    }

    [Fact]
    public async Task ConvertNonMtfFile_ShouldReturnErrorCode()
    {
        // Arrange
        var textFile = Path.Combine(_tempOutputPath, "test.txt");
        await File.WriteAllTextAsync(textFile, "This is not an MTF file");

        // Act
        var result = await RunConverter(["-i", textFile, "-o", _tempOutputPath]);

        // Assert
        result.ShouldBe(1, "Converter should exit with error code for non-MTF file");
    }

    [Fact]
    public async Task ConvertWithMissingArguments_ShouldReturnErrorCode()
    {
        // Act
        var result = await RunConverter(["-i", "somefile.mtf"]); // Missing output argument

        // Assert
        result.ShouldNotBe(0, "Converter should exit with error code for missing arguments");
    }

    [Fact]
    public async Task ConvertToNonExistentDirectory_ShouldCreateDirectory()
    {
        // Arrange
        var inputFile = Path.Combine(_testDataPath, "LCT-1V.mtf");
        var newOutputPath = Path.Combine(_tempOutputPath, "NewDirectory");

        // Act
        var result = await RunConverter(["-i", inputFile, "-o", newOutputPath]);

        // Assert
        result.ShouldBe(0, "Converter should exit with success code");
        Directory.Exists(newOutputPath).ShouldBeTrue("Output directory should be created");
        
        var expectedOutputFile = Path.Combine(newOutputPath, "LCT-1V.json");
        File.Exists(expectedOutputFile).ShouldBeTrue("JSON output file should be created in new directory");
    }

    [Fact]
    public async Task ConvertEmptyDirectory_ShouldHandleGracefully()
    {
        // Arrange
        var emptyDirectory = Path.Combine(_tempOutputPath, "EmptyDirectory");
        Directory.CreateDirectory(emptyDirectory);

        // Act
        var result = await RunConverter(["-i", emptyDirectory, "-o", _tempOutputPath]);

        // Assert
        result.ShouldBe(0, "Converter should exit with success code for empty directory");
    }

    private static async Task<int> RunConverter(string[] args)
    {
        // Redirect console output to capture it during tests
        var originalOut = Console.Out;
        var originalError = Console.Error;
        
        try
        {
            using var stringWriter = new StringWriter();
            using var errorWriter = new StringWriter();
            Console.SetOut(stringWriter);
            Console.SetError(errorWriter);

            return await Program.Main(args);
        }
        finally
        {
            Console.SetOut(originalOut);
            Console.SetError(originalError);
        }
    }

    public void Dispose()
    {
        // Clean up temp directory
        if (Directory.Exists(_tempOutputPath))
        {
            Directory.Delete(_tempOutputPath, true);
        }
    }
}
