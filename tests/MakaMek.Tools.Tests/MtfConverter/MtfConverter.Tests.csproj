<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <AssemblyName>MakaMek.Tools.MtfConverter.Tests</AssemblyName>
        <RootNamespace>MakaMek.Tools.MtfConverter.Tests</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
        <PackageReference Include="xunit" Version="2.9.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Shouldly" Version="4.2.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\src\MakaMek.Tools\MtfConverter\MtfConverter.csproj" />
        <ProjectReference Include="..\..\..\src\MakaMek.Core\MakaMek.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Include="TestData\**" CopyToOutputDirectory="PreserveNewest" />
    </ItemGroup>

</Project>
